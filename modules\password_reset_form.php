<?php
/**
 * Secure Password Reset Form
 * Handles token-based password reset from email links
 */

session_start();
require '../config/config.php';
require_once __DIR__ . '/../includes/PasswordResetService.php';

// Get token from URL and clean it
$rawToken = $_GET['token'] ?? '';
$token = trim(urldecode($rawToken)); // Properly decode URL-encoded token
$message = '';
$messageType = 'error';
$tokenValid = false;
$tokenData = null;

try {
    $passwordResetService = new PasswordResetService($conn);

    // Validate token
    if (!empty($token)) {
        // Check if table exists first
        $tableCheck = $conn->query("SHOW TABLES LIKE 'password_reset_tokens'");
        if ($tableCheck->num_rows === 0) {
            $message = "Password reset system is not properly configured. Please contact your administrator.";
        } else {
            $tokenData = $passwordResetService->validateToken($token);
            if ($tokenData) {
                $tokenValid = true;
            } else {
                $message = "Invalid or expired password reset link. Please request a new password reset.";
            }
        }
    } else {
        $message = "No password reset token provided.";
    }

} catch (Exception $e) {
    $message = "System error: " . $e->getMessage();
    error_log("Password reset form error: " . $e->getMessage());
}

// Handle password reset form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $tokenValid) {
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($newPassword) || empty($confirmPassword)) {
        $message = "Please fill in both password fields.";
        $messageType = 'error';
    } elseif ($newPassword !== $confirmPassword) {
        $message = "Passwords do not match. Please try again.";
        $messageType = 'error';
    } else {
        // Reset password using the service
        $resetResult = $passwordResetService->resetPassword($token, $newPassword);
        
        if ($resetResult['success']) {
            $message = "Password reset successfully! You can now log in with your new password.";
            $messageType = 'success';
            $tokenValid = false; // Hide form after successful reset
            
            // Clear any first-login session data
            unset($_SESSION['first_login_user_id'], $_SESSION['first_login_role']);
            
        } else {
            $message = $resetResult['message'];
            $messageType = 'error';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - UTHM Attendance</title>
    
    <!-- ─── GLOBAL CSS (same as other dashboards) ─── -->
    <link rel="stylesheet" href="../dashboard/css/base-styles.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-header.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-sidebar.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-footer.css" />
    <link rel="stylesheet" href="../dashboard/css/lecturer-dashboard-styles.css" />
    
    <!-- ─── ICONS ─── -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* ─────────── PASSWORD RESET FORM SPECIFIC STYLES ─────────── */
        .reset-container {
            max-width: 500px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .reset-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
        }

        .reset-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .reset-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .reset-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .reset-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .alert-message {
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .alert-success {
            background-color: #d1f2eb;
            border: 1px solid #7dcea0;
            color: #0e6b47;
        }

        .alert-error {
            background-color: #fadbd8;
            border: 1px solid #f1948a;
            color: #922b21;
        }
        .token-info {
            background: var(--bg-light);
            padding: var(--spacing-md);
            border-radius: var(--border-radius);
            margin-bottom: var(--spacing-lg);
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .password-input-container {
            position: relative;
            width: 100%;
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            padding-right: 45px; /* Space for eye icon */
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            font-size: 1.1rem;
            padding: 4px;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            border: 2px solid #3b82f6;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 48px;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
            margin-bottom: var(--spacing-md);
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border-color: #2563eb;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .secondary-btn {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            border-color: #6b7280;
        }

        .secondary-btn:hover {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
            border-color: #4b5563;
        }

        .password-requirements {
            background: #f8f9fa;
            padding: var(--spacing-lg);
            border-radius: var(--border-radius);
            margin: var(--spacing-lg) 0;
            border: 1px solid #e9ecef;
        }

        .requirements-title {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 0.95rem;
        }

        .requirements-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .requirement-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: 6px 0;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .requirement-icon {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
        }

        .requirement-valid {
            color: #22c55e;
        }

        .requirement-invalid {
            color: #ef4444;
        }

        .password-strength {
            margin-top: var(--spacing-sm);
            font-size: 0.85rem;
            font-weight: 600;
        }

        .strength-weak {
            color: #ef4444;
        }

        .strength-medium {
            color: #f59e0b;
        }

        .strength-strong {
            color: #22c55e;
        }

        .back-link {
            display: block;
            text-align: center;
            margin-top: var(--spacing-lg);
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-card">
            <div class="reset-header">
                <div class="reset-icon">
                    <i class="fas fa-key"></i>
                </div>
                <h1 class="reset-title">Reset Your Password</h1>
                <p class="reset-subtitle">UTHM Attendance System</p>
            </div>

            <?php if ($message): ?>
                <div class="alert-message alert-<?php echo $messageType; ?>">
                    <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-circle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($tokenValid && $tokenData): ?>

                <form method="POST" id="resetForm">
                    <div class="form-group">
                        <label for="new_password" class="form-label">
                            <i class="fas fa-lock"></i>
                            New Password:
                        </label>
                        <div class="password-input-container">
                            <input type="password" class="form-input" id="new_password" name="new_password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye" id="new_password_icon"></i>
                            </button>
                        </div>
                        <div id="password_strength" class="password-strength" style="display: none;"></div>
                    </div>

                    <div class="password-requirements">
                        <div class="requirements-title">
                            <i class="fas fa-info-circle"></i>
                            Password Requirements:
                        </div>
                        <ul class="requirements-list">
                            <li class="requirement-item" id="req_length">
                                <span class="requirement-icon">
                                    <i class="fas fa-times requirement-invalid"></i>
                                </span>
                                At least 8 characters
                            </li>
                            <li class="requirement-item" id="req_uppercase">
                                <span class="requirement-icon">
                                    <i class="fas fa-times requirement-invalid"></i>
                                </span>
                                One uppercase letter
                            </li>
                            <li class="requirement-item" id="req_lowercase">
                                <span class="requirement-icon">
                                    <i class="fas fa-times requirement-invalid"></i>
                                </span>
                                One lowercase letter
                            </li>
                            <li class="requirement-item" id="req_number">
                                <span class="requirement-icon">
                                    <i class="fas fa-times requirement-invalid"></i>
                                </span>
                                One number
                            </li>
                            <li class="requirement-item" id="req_special">
                                <span class="requirement-icon">
                                    <i class="fas fa-times requirement-invalid"></i>
                                </span>
                                One special character
                            </li>
                        </ul>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password" class="form-label">
                            <i class="fas fa-lock"></i>
                            Confirm New Password:
                        </label>
                        <div class="password-input-container">
                            <input type="password" class="form-input" id="confirm_password" name="confirm_password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirm_password_icon"></i>
                            </button>
                        </div>
                        <div id="password_match" class="password-strength" style="display: none;"></div>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-save"></i>
                        Reset Password
                    </button>
                </form>
            <?php endif; ?>

            <?php if ($messageType === 'success' || !$tokenValid): ?>
                <a href="../index.php" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    Back to Login
                </a>
            <?php endif; ?>

            <div style="text-align: center; margin-top: var(--spacing-lg); color: var(--text-secondary); font-size: 0.8rem;">
                <i class="fas fa-clock"></i>
                Generated on <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
    </div>

    <script>
        // Password visibility toggle
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + '_icon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        // Password validation and real-time feedback
        function validatePassword(password) {
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password),
                special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
            };

            // Update requirement indicators
            updateRequirement('req_length', requirements.length);
            updateRequirement('req_uppercase', requirements.uppercase);
            updateRequirement('req_lowercase', requirements.lowercase);
            updateRequirement('req_number', requirements.number);
            updateRequirement('req_special', requirements.special);

            // Calculate password strength
            const validCount = Object.values(requirements).filter(Boolean).length;
            let strength = 'weak';
            let strengthClass = 'strength-weak';

            if (validCount >= 5) {
                strength = 'strong';
                strengthClass = 'strength-strong';
            } else if (validCount >= 3) {
                strength = 'medium';
                strengthClass = 'strength-medium';
            }

            // Update strength indicator
            const strengthElement = document.getElementById('password_strength');
            if (password.length > 0) {
                strengthElement.style.display = 'block';
                strengthElement.textContent = `Password Strength: ${strength.charAt(0).toUpperCase() + strength.slice(1)}`;
                strengthElement.className = `password-strength ${strengthClass}`;
            } else {
                strengthElement.style.display = 'none';
            }

            return Object.values(requirements).every(Boolean);
        }

        function updateRequirement(elementId, isValid) {
            const element = document.getElementById(elementId);
            const icon = element.querySelector('.requirement-icon i');

            if (isValid) {
                icon.className = 'fas fa-check requirement-valid';
            } else {
                icon.className = 'fas fa-times requirement-invalid';
            }
        }

        function validatePasswordMatch() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const matchElement = document.getElementById('password_match');

            if (confirmPassword.length > 0) {
                matchElement.style.display = 'block';
                if (newPassword === confirmPassword) {
                    matchElement.textContent = 'Passwords match';
                    matchElement.className = 'password-strength strength-strong';
                } else {
                    matchElement.textContent = 'Passwords do not match';
                    matchElement.className = 'password-strength strength-weak';
                }
            } else {
                matchElement.style.display = 'none';
            }
        }

        // Event listeners
        document.getElementById('new_password').addEventListener('input', function() {
            validatePassword(this.value);
            validatePasswordMatch();
        });

        document.getElementById('confirm_password').addEventListener('input', function() {
            validatePasswordMatch();
        });

        // Form submission validation
        document.getElementById('resetForm').addEventListener('submit', function(e) {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match. Please try again.');
                return false;
            }

            if (!validatePassword(newPassword)) {
                e.preventDefault();
                alert('Password does not meet all requirements. Please check the password requirements and try again.');
                return false;
            }
        });
    </script>
</body>
</html>
